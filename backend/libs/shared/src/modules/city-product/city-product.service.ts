import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { CityProductRepository } from '../../repositories/city-product.repository';
import { CityProduct } from '../../repositories/models/cityProduct.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { CityService } from '../city/city.service';
import { ProductService } from '../product/product.service';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';

@Injectable()
export class CityProductService {
  constructor(
    private readonly cityProductRepository: CityProductRepository,
    private readonly cityService: CityService,
    private readonly productService: ProductService,
    private readonly fileUploadService: FileUploadService,
  ) { }

  /**
   * Add multiple products to a city
   * @param cityId - City ID
   * @param productData - Array of product data with productId and vehicleTypeId
   */
  async addProductsToCity(
    cityId: string,
    productData: Array<{ productId: string; vehicleTypeId: string }>,
  ): Promise<CityProduct[]> {
    // Verify city exists
    await this.cityService.findCityById(cityId);

    const results: CityProduct[] = [];

    for (const { productId, vehicleTypeId } of productData) {
      // Verify product exists
      await this.productService.findProductById(productId);

      // Check if city product already exists
      const existingCityProduct =
        await this.cityProductRepository.findCityProductByCityProductVehicle(
          cityId,
          productId,
          vehicleTypeId,
        );

      if (existingCityProduct) {
        // Skip if already exists, or update if needed
        results.push(existingCityProduct);
      } else {
        // Create new city product with isEnabled = false
        const newCityProduct =
          await this.cityProductRepository.createCityProduct({
            cityId,
            productId,
            vehicleTypeId,
            isEnabled: false,
          });
        results.push(newCityProduct);
      }
    }

    return results;
  }

  /**
   * Remove multiple products from a city
   * @param cityId - City ID
   * @param productData - Array of product data with productId and vehicleTypeId
   */
  async removeProductsFromCity(
    cityId: string,
    productData: Array<{ productId: string; vehicleTypeId: string }>,
  ): Promise<void> {
    // Verify city exists
    await this.cityService.findCityById(cityId);

    const cityProductsToDelete: string[] = [];

    for (const { productId, vehicleTypeId } of productData) {
      const existingCityProduct =
        await this.cityProductRepository.findCityProductByCityProductVehicle(
          cityId,
          productId,
          vehicleTypeId,
        );

      if (existingCityProduct) {
        cityProductsToDelete.push(existingCityProduct.id);
      }
    }

    if (cityProductsToDelete.length > 0) {
      await this.cityProductRepository.deleteCityProductsByIds(
        cityProductsToDelete,
      );
    }
  }

  /**
   * Enable a city product
   * @param id - CityProduct ID
   */
  async enableCityProduct(id: string): Promise<CityProduct> {
    const cityProduct =
      await this.cityProductRepository.findCityProductById(id);
    if (!cityProduct) {
      throw new NotFoundException(`City product with ID ${id} not found`);
    }

    // Check if the product itself is enabled
    const product = await this.productService.findProductById(
      cityProduct.productId,
    );
    if (!product.isEnabled) {
      throw new BadRequestException(
        `Cannot enable city product because the product "${product.name}" is disabled`,
      );
    }

    return this.cityProductRepository.updateCityProduct(id, {
      isEnabled: true,
    });
  }

  /**
   * Disable a city product
   * @param id - CityProduct ID
   */
  async disableCityProduct(id: string): Promise<CityProduct> {
    const cityProduct =
      await this.cityProductRepository.findCityProductById(id);
    if (!cityProduct) {
      throw new NotFoundException(`City product with ID ${id} not found`);
    }

    return this.cityProductRepository.updateCityProduct(id, {
      isEnabled: false,
    });
  }

  /**
   * Paginate city products with filters
   * @param cityId - City ID
   * @param page - Page number
   * @param limit - Items per page
   * @param dto - Pagination and filter options
   */
  async paginateCityProducts(
    cityId: string,
    page = 1,
    limit = 10,
    dto?: PaginationDto & { productName?: string; vehicleTypeId?: string },
  ) {
    // Verify city exists
    // await this.cityService.findCityById(cityId);

    const options = this.buildPaginateOptions(dto);
    options.where = { cityId, ...options.where };
    const result =
      await this.cityProductRepository.paginateCityProductsByCityId(
        cityId,
        page,
        Number(limit),
        options,
      );

    // Add signed URLs to product icons
    const dataWithSignedUrls = await Promise.all(
      result.data.map(async (cityProduct) => {
        if (cityProduct.product?.icon) {
          try {
            const signedUrl = await this.fileUploadService.getSignedUrl(
              cityProduct.product.icon,
              3600, // 1 hour expiry
            );
            return {
              ...cityProduct,
              product: {
                ...cityProduct.product,
                icon: signedUrl,
              },
            };
          } catch (error) {
            // If signed URL generation fails, keep the original URL
            return cityProduct;
          }
        }
        return cityProduct;
      }),
    );

    return {
      ...result,
      data: dataWithSignedUrls,
    };
  }

  /**
   * Disable all city products for a city
   * @param cityId - City ID
   */
  async disableAllCityProducts(cityId: string): Promise<void> {
    // Verify city exists
    await this.cityService.findCityById(cityId);

    await this.cityProductRepository.updateAllCityProductsStatusByCityId(
      cityId,
      false,
    );
  }

  /**
   * Enable all city products for a city
   * @param cityId - City ID
   */
  async enableAllCityProducts(cityId: string): Promise<void> {
    // Verify city exists
    await this.cityService.findCityById(cityId);

    await this.cityProductRepository.updateAllCityProductsStatusByCityId(
      cityId,
      true,
    );
  }

  /**
   * Get city products by multiple city IDs
   * @param cityIds - Array of city IDs
   */
  async getCityProductsByCityIds(cityIds: string[]): Promise<CityProduct[]> {
    // Validate that all city IDs exist
    for (const cityId of cityIds) {
      await this.cityService.findCityById(cityId);
    }

    const cityProducts = await this.cityProductRepository.findCityProductsByCityIds(cityIds);

    // Add signed URLs to product icons
    const dataWithSignedUrls = await Promise.all(
      cityProducts.map(async (cityProduct) => {
        if (cityProduct.product?.icon) {
          try {
            const signedUrl = await this.fileUploadService.getSignedUrl(
              cityProduct.product.icon,
              3600, // 1 hour expiry
            );
            return {
              ...cityProduct,
              product: {
                ...cityProduct.product,
                icon: signedUrl,
              },
            };
          } catch (error) {
            // If signed URL generation fails, keep the original URL
            return cityProduct;
          }
        }
        return cityProduct;
      }),
    );

    return dataWithSignedUrls;
  }

  /**
   * Find city product by ID
   * @param id - CityProduct ID
   */
  async findCityProductById(id: string): Promise<CityProduct> {
    const cityProduct =
      await this.cityProductRepository.findCityProductById(id);
    if (!cityProduct) {
      throw new NotFoundException(`City product with ID ${id} not found`);
    }

    // Add signed URL to product icon if it exists
    if (cityProduct.product?.icon) {
      try {
        const signedUrl = await this.fileUploadService.getSignedUrl(
          cityProduct.product.icon,
          3600, // 1 hour expiry
        );
        return {
          ...cityProduct,
          product: {
            ...cityProduct.product,
            icon: signedUrl,
          },
        };
      } catch (error) {
        // If signed URL generation fails, keep the original URL
        return cityProduct;
      }
    }

    return cityProduct;
  }

  /**
   * Build options for pagination with filters
   */
  private buildPaginateOptions(
    dto?: PaginationDto & { productName?: string; vehicleTypeId?: string },
  ) {
    const options: any = {};

    if (dto) {
      const whereConditions: any = {};

      // Search by product name
      if (dto.search || dto.productName) {
        const searchTerm = dto.search || dto.productName;
        whereConditions.product = {
          name: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        };
      }

      // Filter by vehicle type
      if (dto.vehicleTypeId) {
        whereConditions.vehicleTypeId = dto.vehicleTypeId;
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }

      // Sorting
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      } else {
        // Default sorting by createdAt desc if no sortBy is provided
        options.orderBy = { createdAt: 'desc' };
      }
    } else {
      // Default sorting when no dto is provided
      options.orderBy = { createdAt: 'desc' };
    }

    return options;
  }
}
