import * as crypto from 'crypto';
(global as any).crypto = crypto;
import { NestFactory } from '@nestjs/core';
import { ApiModule } from './api.module';
import { AppConfigService } from '@shared/shared/config';
import helmet from 'helmet';
import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { setupSwagger } from './docs/swagger';
import { RedisIoAdapter } from './adapters/redis-io.adapter';

async function bootstrap() {
  const app = await NestFactory.create(ApiModule, {
    cors: true,
  });

  const configService = app.get(AppConfigService);
  const port = configService.coreApiPort;
  const host = configService.coreApiHost;
  const logger = new Logger('Bootstrap');

  // app.enableCors({
  //   origin: configService.corsOrigins.split(',').map((origin) => origin.trim()),
  //   methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
  //   credentials: true,
  // });
  app.enableCors();

  // Enable WebSocket adapter for Socket.IO with Redis support
  const redisAdapter = new RedisIoAdapter(app, configService);
  await redisAdapter.connectToRedis();
  app.useWebSocketAdapter(redisAdapter);

  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
        },
      },
    }),
  );

  // Enable versioning
  app.enableVersioning({
    type: VersioningType.URI,
    prefix: 'v',
    defaultVersion: '1',
  });

  app.setGlobalPrefix('api');

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // Setup Swagger documentation
  setupSwagger(app);

  await app.listen(port, host);
  logger.log(`Tuxi Core api is running on ${await app.getUrl()}`);
  logger.log(`Swagger documentation available at ${await app.getUrl()}/docs`);
}
void bootstrap();
