import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { Zone } from '@shared/shared/repositories/models/zone.model';
import { ZoneRepository } from '@shared/shared/repositories/zone.repository';
import { H3IndexToZoneRepository } from '@shared/shared/repositories/h3IndexToZone.repository';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';
import { H3UtilityService } from '@shared/shared/common/h3-utility/h3-utility.service';
import { CreateZoneInputDto, UpdateZoneInputDto } from './zone-input.dto';
import { ZoneResponseDto, PaginatedZoneResponseDto } from './zone-response.dto';

@Injectable()
export class ZoneService {
  constructor(
    private readonly zoneRepository: ZoneRepository,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly prisma: PrismaService,
    private readonly h3UtilityService: H3UtilityService,
  ) {}

  /**
   * Create a new zone
   */
  async create(data: CreateZoneInputDto): Promise<ZoneResponseDto> {
    // Check if zone name already exists in the city
    const zoneNameTrimmed = data.name.trim();
    data.name = zoneNameTrimmed;

    const nameExists = await this.zoneRepository.nameExists(
      zoneNameTrimmed,
      data.cityId,
    );
    if (nameExists) {
      throw new ConflictException(
        `Zone with name '${data.name}' already exists in this city`,
      );
    }

    const zone = await this.zoneRepository.create(data);
    return this.mapToResponseDto(zone);
  }

  /**
   * Get all zones with optional filters
   */
  async findAll(options?: {
    cityId?: string;
    zoneTypeId?: string;
    includeRelations?: boolean;
  }): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findAll(options);
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Get zone by ID
   */
  async findById(
    id: string,
    options?: { includeRelations?: boolean },
  ): Promise<ZoneResponseDto> {
    const zone = await this.zoneRepository.findById(id, options);
    if (!zone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return this.mapToResponseDto(zone);
  }

  /**
   * Get zone by name within a city
   */
  async findByName(
    name: string,
    cityId?: string,
  ): Promise<ZoneResponseDto | null> {
    const zone = await this.zoneRepository.findByName(name, cityId);
    return zone ? this.mapToResponseDto(zone) : null;
  }

  /**
   * Get zones by city ID (excludes city zones - isCity: false only)
   */
  async findByCityId(
    cityId: string,
    options?: { includeRelations?: boolean },
  ): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findByCityId(cityId, options);
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Get all zones by city ID (includes both city zones and regular zones)
   */
  async findAllZonesByCityId(
    cityId: string,
    options?: { includeRelations?: boolean },
  ): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findAllZonesByCityId(
      cityId,
      options,
    );
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Get all zones by multiple city IDs (includes both city zones and regular zones)
   */
  async findAllZonesByMultipleCityIds(
    cityIds: string[],
    options?: { includeRelations?: boolean },
  ): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findAllZonesByMultipleCityIds(
      cityIds,
      options,
    );
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Get zones by zone type ID
   */
  async findByZoneTypeId(zoneTypeId: string): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findByZoneTypeId(zoneTypeId);
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Get zones by H3 index
   */
  async findByH3Index(h3Index: string): Promise<ZoneResponseDto[]> {
    const zones = await this.zoneRepository.findByH3Index(h3Index);
    return zones.map((zone) => this.mapToResponseDto(zone));
  }

  /**
   * Update zone by ID
   */
  async update(id: string, data: UpdateZoneInputDto): Promise<ZoneResponseDto> {
    // Check if name is being changed and if it conflicts
    const zone = await this.zoneRepository.findById(id);
    if (!zone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    if (data.name) {
      // Check if zone name already exists in the city
      const zoneNameTrimmed = data.name.trim();
      data.name = zoneNameTrimmed;

      const nameExists = await this.zoneRepository.nameExists(
        zoneNameTrimmed,
        zone.cityId || '',
        id, // Exclude current zone
      );
      if (nameExists) {
        throw new ConflictException(
          `Zone with name '${data.name}' already exists in this city`,
        );
      }
    }

    if (data.priority && zone.cityId) {
      const priorityExists = await this.zoneRepository.priorityExists(
        data.priority,
        zone.cityId,
      );
      if (priorityExists && priorityExists.id !== zone.id) {
        throw new ConflictException(`Priority ${data.priority} already exists`);
      }
    }

    const updatedZone = await this.zoneRepository.update(id, data);
    if (!updatedZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return this.mapToResponseDto(updatedZone);
  }

  /**
   * Soft delete zone by ID
   */
  async softDelete(id: string): Promise<ZoneResponseDto> {
    const deletedZone = await this.zoneRepository.softDelete(id);
    if (!deletedZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return this.mapToResponseDto(deletedZone);
  }

  /**
   * Hard delete zone by ID (use with caution)
   */
  async hardDelete(id: string): Promise<boolean> {
    const result = await this.zoneRepository.hardDelete(id);
    if (!result) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return result;
  }

  /**
   * Restore soft-deleted zone
   */
  async restore(id: string): Promise<ZoneResponseDto> {
    const restoredZone = await this.zoneRepository.restore(id);
    if (!restoredZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return this.mapToResponseDto(restoredZone);
  }

  /**
   * Get paginated zones
   */
  async findPaginated(
    page: number = 1,
    limit: number = 10,
    filters?: {
      search?: string;
      cityId?: string;
      zoneTypeId?: string;
      includeRelations?: boolean;
    },
  ): Promise<PaginatedZoneResponseDto> {
    if (page < 1) {
      throw new BadRequestException('Page number must be greater than 0');
    }
    if (limit < 1 || limit > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }

    const result = await this.zoneRepository.findPaginated(
      page,
      limit,
      filters,
    );

    return {
      ...result,
      data: result.data.map((zone) => this.mapToResponseDto(zone)),
    };
  }

  /**
   * Check if zone name exists in city
   */
  async checkNameExists(
    name: string,
    cityId: string,
    excludeId?: string,
  ): Promise<boolean> {
    return this.zoneRepository.nameExists(name, cityId, excludeId);
  }

  // ===== ORIGINAL LEGACY FUNCTIONS (used by city service and other parts) =====

  /**
   * Create a zone (legacy function used by createCityZone)
   */
  async createZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    return this.prisma.$transaction(async () => {
      // Create the zone using repository
      const { h3Indexes, ...zoneData } = data;

      const zone = await this.zoneRepository.createLegacyZone(zoneData);

      // Create H3 index mappings (convert strings to BigInt for lookup table)
      if (data.h3Indexes && data.h3Indexes.length > 0) {
        const h3BigIntIndexes = this.h3UtilityService.stringArrayToBigIntArray(
          data.h3Indexes,
        );
        await this.h3IndexToZoneRepository.createManyH3IndexToZones(
          h3BigIntIndexes.map((h3Index) => ({
            h3Index,
            zoneId: zone.id,
          })),
        );
      }

      return zone;
    });
  }

  /**
   * Create city zone (used by city service)
   */
  async createCityZone(
    name: string,
    cityId: string,
    polygon: any,
    h3Indexes: string[],
  ): Promise<Zone> {
    const zoneData = {
      name,
      isCity: true,
      polygon,
      h3Indexes,
      cityId,
    };

    return this.createZone(zoneData);
  }

  /**
   * Find all zones (legacy function)
   */
  async findAllZones(): Promise<Zone[]> {
    return this.zoneRepository.findAllZones();
  }

  /**
   * Find zone by ID (legacy function)
   */
  async findZoneById(id: string): Promise<Zone> {
    const zone = await this.zoneRepository.findZoneById(id);
    if (!zone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return zone;
  }

  /**
   * Find zones by city (legacy function)
   */
  async findZonesByCity(cityId: string): Promise<Zone[]> {
    return this.zoneRepository.findZonesByCity(cityId);
  }

  /**
   * Find city zone (legacy function)
   */
  async findCityZone(cityId: string): Promise<Zone | null> {
    return this.zoneRepository.findCityZone(cityId);
  }

  /**
   * Find zones by H3 index (legacy function)
   */
  async findZonesByH3Index(h3Index: string): Promise<Zone[]> {
    return this.zoneRepository.findZonesByH3Index(h3Index);
  }

  /**
   * Update zone (legacy function)
   */
  async updateZone(id: string, data: Partial<Zone>): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Update the zone
      const { h3Indexes, ...zoneData } = data;
      const updatedZone = await this.zoneRepository.updateZone(id, zoneData);

      // Update H3 index mappings if h3Indexes are provided
      if (h3Indexes !== undefined) {
        const h3BigIntIndexes =
          this.h3UtilityService.stringArrayToBigIntArray(h3Indexes);
        await this.h3IndexToZoneRepository.bulkUpsertH3IndexToZones(
          id,
          h3BigIntIndexes,
        );
      }

      return updatedZone;
    });
  }

  /**
   * Delete zone (legacy function)
   */
  async deleteZone(id: string): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Delete H3 index mappings first
      await this.h3IndexToZoneRepository.deleteH3IndexToZonesByZone(id);

      // Then delete the zone
      return this.zoneRepository.deleteZone(id);
    });
  }

  /**
   * Paginate zones (legacy function)
   */
  async paginateZones(
    page = 1,
    limit = 10,
    filters?: {
      isCity?: boolean;
      cityId?: string;
      search?: string;
    },
  ) {
    const options: any = {};

    if (filters) {
      const whereConditions: any = {};

      if (filters.isCity !== undefined) {
        whereConditions.isCity = filters.isCity;
      }

      if (filters.cityId) {
        whereConditions.cityId = filters.cityId;
      }

      if (filters.search) {
        whereConditions.name = {
          contains: filters.search,
          mode: 'insensitive',
        };
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }
    }

    return this.zoneRepository.paginateZones(page, limit, options);
  }

  /**
   * Map zone model to response DTO
   */
  private mapToResponseDto(zone: Zone): ZoneResponseDto {
    return {
      id: zone.id,
      name: zone.name,
      description: zone.description ?? null,
      polygon: zone.polygon ?? null,
      meta: zone.meta ?? null,
      priority: zone.priority ?? 100,
      zoneTypeId: zone.zoneTypeId ?? null,
      cityId: zone.cityId ?? null,
      isCity: zone.isCity,
      h3Indexes: zone.h3Indexes || [],
      createdAt: zone.createdAt,
      updatedAt: zone.updatedAt,
      deletedAt: zone.deletedAt ?? null,
      // Include zoneType if available
      ...(zone.zoneType && { zoneType: zone.zoneType }),
    };
  }
}
