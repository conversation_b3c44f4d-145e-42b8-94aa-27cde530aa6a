import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  City,
  CityStatus,
} from '@shared/shared/repositories/models/city.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { CityRepository } from '@shared/shared/repositories/city.repository';
import { ApiConsumer } from '../auth/interfaces';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';
import { ZoneService } from '../zone/zone.service';
import {
  H3UtilityService,
  LatLng,
} from '@shared/shared/common/h3-utility/h3-utility.service';
import { PrismaService } from '@shared/shared/database/prisma/prisma.service';

@Injectable()
export class CityService {
  constructor(
    private readonly cityRepository: CityRepository,
    private readonly fileUploadService: FileUploadService,
    private readonly zoneService: ZoneService,
    private readonly h3UtilityService: H3UtilityService,
    private readonly prisma: PrismaService,
    private readonly cityAdminRepository: CityAdminRepository,
  ) {}

  async createCity(
    data: Omit<
      City,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'status' | 'h3Indexes'
    > & {
      status?: CityStatus;
      polygon?: LatLng[];
    },
  ): Promise<City> {
    const cityNameTrimmed = data.name.trim();
    data.name = cityNameTrimmed;
    const existingCity = await this.cityRepository.findCityByNameAndState(
      cityNameTrimmed,
      data.state || undefined,
    );

    if (existingCity) {
      throw new BadRequestException(
        `City with name "${data.name}" already exists in state "${data.state || 'N/A'}"`,
      );
    }

    return this.prisma.$transaction(async () => {
      let geoJsonPolygon: any = null;
      let h3Indexes: string[] = [];

      if (data.polygon && data.polygon.length > 0) {
        this.h3UtilityService.validateCoordinates(data.polygon);

        geoJsonPolygon = this.h3UtilityService.coordinatesToGeoJsonPolygon(
          data.polygon,
        );

        h3Indexes = this.h3UtilityService.polygonToH3Indexes(geoJsonPolygon, 8);
      }

      const cityData = {
        ...data,
        polygon: data.polygon ? geoJsonPolygon : null,
        h3Indexes,
        status: data.status || CityStatus.ACTIVE,
      };
      delete (cityData as any).polygon;
      cityData.polygon = geoJsonPolygon;

      const city = await this.cityRepository.createCity(cityData);

      // Create corresponding zone if polygon is provided
      if (geoJsonPolygon && h3Indexes.length > 0) {
        await this.zoneService.createCityZone(
          city.name,
          city.id,
          geoJsonPolygon,
          h3Indexes,
        );
      }

      return this.addSignedUrlToCity(city);
    });
  }

  async findAllActiveCities(): Promise<City[]> {
    const cities = await this.cityRepository.findAllActiveCities();
    return this.addSignedUrlsToCities(cities);
  }

  /**
   * Find all active cities with role-based access control
   */
  async findAllActiveCitiesForUser(apiConsumer: ApiConsumer): Promise<City[]> {
    // Check if user has super_admin or sub_admin roles
    const isSuperOrSubAdmin = apiConsumer.roles.some(
      (role) => role === 'super_admin' || role === 'sub_admin',
    );

    if (isSuperOrSubAdmin) {
      // Super admin and sub admin can see all cities
      return this.findAllActiveCities();
    }

    // Check if user is city_admin
    const isCityAdmin = apiConsumer.roles.includes('city_admin');
    if (isCityAdmin) {
      // Get cities where this user is assigned as city admin
      const cityAdmins = await this.cityAdminRepository.findMany({
        where: {
          userProfileId: apiConsumer.profileId,
          deletedAt: null,
          isEnabled: true,
        },
        select: {
          cityId: true,
        },
      });

      const cityIds = cityAdmins.map((ca) => ca.cityId);

      if (cityIds.length === 0) {
        // City admin with no assigned cities
        return [];
      }

      // Get cities by IDs
      const cities = await this.cityRepository.findMany({
        where: {
          id: {
            in: cityIds,
          },
          status: CityStatus.ACTIVE,
        },
      });

      return this.addSignedUrlsToCities(cities);
    }

    // If user doesn't have appropriate roles, return empty array
    return [];
  }

  async findAllCities(): Promise<City[]> {
    const cities = await this.cityRepository.findAllCities();
    // Return cities without adding signed URLs for icons
    return cities;
  }

  async findCitiesByGeoCoordinates(
    lat: number,
    lng: number,
    radiusInMeter: number,
  ): Promise<City[]> {
    // Convert lat/lng to H3 index
    const h3Index = this.h3UtilityService.coordinatesToH3Index(lat, lng, 8);

    // Convert radius to ring size (approximate)
    // H3 resolution 8: ~0.46 km² per cell, ~0.76 km edge length
    const ringSize = Math.max(1, Math.ceil(radiusInMeter / 760)); // 760m is approximate edge length for res 8
    // Get all H3 indexes within the radius
    const h3Indexes = this.h3UtilityService.getNeighboringCells(
      h3Index,
      ringSize,
    );
    // Find cities that contain any of these H3 indexes
    const cities = await this.cityRepository.findCitiesByH3Indexes(h3Indexes);

    // Return cities without icon (as requested) and with specific fields
    return cities;
  }

  async findCityById(id: string): Promise<City> {
    const city = await this.cityRepository.findCityById(id);
    if (!city) throw new NotFoundException(`City with ID ${id} not found`);
    return this.addSignedUrlToCity(city);
  }

  async updateCity(id: string, data: Partial<City>): Promise<City> {
    // Check if city exists
    const existingCity = await this.cityRepository.findCityById(id);
    if (!existingCity) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    // Check for duplicate name and state combination (excluding current record)
    if (data.name || data.state !== undefined) {
      const nameToCheck = data.name || existingCity.name;
      const cityNameTrimmed = nameToCheck.trim();

      const stateToCheck =
        data.state !== undefined ? data.state : existingCity.state;
      data.name = cityNameTrimmed;

      const duplicateCity = await this.cityRepository.findCityByNameAndState(
        cityNameTrimmed,
        stateToCheck || undefined,
      );
      if (duplicateCity && duplicateCity.id !== id) {
        throw new BadRequestException(
          `City with name "${cityNameTrimmed}" already exists in state "${stateToCheck || 'N/A'}"`,
        );
      }
    }

    // Handle icon deletion if icon is being updated and old icon exists
    if (
      data.icon !== undefined &&
      existingCity.icon &&
      data.icon !== existingCity.icon
    ) {
      try {
        await this.fileUploadService.deleteFile(existingCity.icon);
      } catch (error) {
        console.warn(
          `Failed to delete old icon file: ${existingCity.icon}`,
          error,
        );
      }
    }
    return this.prisma.$transaction(async () => {
      let geoJsonPolygon: any = null;
      let h3Indexes: string[] = [];

      if (data.polygon && data.polygon.length > 0) {
        this.h3UtilityService.validateCoordinates(data.polygon);

        geoJsonPolygon = this.h3UtilityService.coordinatesToGeoJsonPolygon(
          data.polygon,
        );

        h3Indexes = this.h3UtilityService.polygonToH3Indexes(geoJsonPolygon, 8);
      }

      const cityData = {
        ...data,
        // polygon: data.polygon ? geoJsonPolygon : null,
        // h3Indexes,
        status: data.status || CityStatus.ACTIVE,
      };
      // delete (cityData as any).polygon;
      // cityData.polygon = geoJsonPolygon;

      const city = await this.cityRepository.updateCity(id, cityData);
      const cityZone = await this.zoneService.findCityZone(id);
      if (cityZone) {
        await this.zoneService.updateZone(cityZone.id, {
          name: city.name,
          polygon: data.polygon,
          h3Indexes,
        });
        console.log(`Updated zone for city ${city.name}`);
      } else {
        await this.zoneService.createCityZone(
          city.name,
          city.id,
          data.polygon,
          h3Indexes,
        );
        console.log(`Created zone for city ${city.name}`);
      }

      return this.addSignedUrlToCity(city);
    });
  }

  async deleteCity(id: string): Promise<City> {
    return this.cityRepository.deleteCity(id);
  }

  async paginateCities(
    page = 1,
    limit = 10,
    dto?: PaginationDto & { state?: string; status?: string; cityId?: string },
  ) {
    const options = this.buildPaginateOptions(dto);
    const result = await this.cityRepository.paginateCities(
      Number(page),
      Number(limit),
      options,
    );

    return {
      ...result,
      data: await this.addSignedUrlsToCities(result.data),
    };
  }

  /**
   * Paginate cities for admin with additional filters and role-based access control
   */
  async paginateCitiesForAdmin(
    page = 1,
    limit = 10,
    dto?: PaginationDto & { state?: string; status?: string; cityId?: string },
    apiConsumer?: ApiConsumer,
  ) {
    // If no apiConsumer provided, use the original method for backward compatibility
    if (!apiConsumer) {
      return this.paginateCities(page, limit, dto);
    }

    // Check if user has super_admin or sub_admin roles
    const isSuperOrSubAdmin = apiConsumer.roles.some(
      (role) => role === 'super_admin' || role === 'sub_admin',
    );

    if (isSuperOrSubAdmin) {
      // Super admin and sub admin can see all cities
      return this.paginateCities(page, limit, dto);
    }

    // Check if user is city_admin
    const isCityAdmin = apiConsumer.roles.includes('city_admin');
    if (isCityAdmin) {
      // Get cities where this user is assigned as city admin
      const cityAdmins = await this.cityAdminRepository.findMany({
        where: {
          userProfileId: apiConsumer.profileId,
          deletedAt: null,
          isEnabled: true,
        },
        select: {
          cityId: true,
        },
      });

      const cityIds = cityAdmins.map((ca) => ca.cityId);

      if (cityIds.length === 0) {
        // City admin with no assigned cities
        return {
          data: [],
          meta: {
            page: Number(page),
            limit: Number(limit),
            total: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPrevPage: false,
          },
        };
      }

      // Build options with city ID filter
      const options = this.buildPaginateOptions(dto);
      options.where = {
        ...options.where,
        id: {
          in: cityIds,
        },
      };

      const result = await this.cityRepository.paginateCities(
        Number(page),
        Number(limit),
        options,
      );

      return {
        ...result,
        data: await this.addSignedUrlsToCities(result.data),
      };
    }

    // If user doesn't have appropriate roles, return empty result
    return {
      data: [],
      meta: {
        page: Number(page),
        limit: Number(limit),
        total: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  }

  /**
   * Change city status (active/inactive)
   */
  async changeCityStatus(id: string, status: CityStatus): Promise<City> {
    const city = await this.cityRepository.findCityById(id);
    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    const updatedCity = await this.cityRepository.updateCityStatus(id, status);
    return this.addSignedUrlToCity(updatedCity);
  }

  /**
   * Build options for pagination, supporting search by name, state filter, status filter, and city ID filter
   */
  private buildPaginateOptions(
    dto?: PaginationDto & { state?: string; status?: string; cityId?: string },
  ) {
    const options: any = {};
    if (dto) {
      const whereConditions: any = {};

      // Search by name
      if (dto.search) {
        whereConditions.name = {
          contains: dto.search,
          mode: 'insensitive',
        };
      }

      // Filter by state
      if (dto.state) {
        whereConditions.state = {
          contains: dto.state,
          mode: 'insensitive',
        };
      }

      // Filter by status
      if (dto.status) {
        whereConditions.status = dto.status;
      }

      // Filter by city ID
      if (dto.cityId) {
        whereConditions.id = dto.cityId;
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }

      // Sorting
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      } else {
        // Default sorting by createdAt desc if no sortBy is provided
        options.orderBy = { createdAt: 'desc' };
      }
    } else {
      // Default sorting when no dto is provided
      options.orderBy = { createdAt: 'desc' };
    }
    return options;
  }

  /**
   * Helper method to add signed URL to single city
   */
  private async addSignedUrlToCity(city: City): Promise<City> {
    if (city.icon) {
      try {
        const signedUrl = await this.fileUploadService.getSignedUrl(
          city.icon,
          3600, // 1 hour expiry
        );
        return { ...city, icon: signedUrl };
      } catch (error) {
        // If signed URL generation fails, keep the original URL
        return city;
      }
    }
    return city;
  }

  /**
   * Helper method to add signed URLs to array of cities
   */
  private async addSignedUrlsToCities(cities: City[]): Promise<City[]> {
    return Promise.all(
      cities.map(async (city) => {
        return this.addSignedUrlToCity(city);
      }),
    );
  }
}
